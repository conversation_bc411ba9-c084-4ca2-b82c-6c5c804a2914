"use client";

import { useState, useEffect } from "react";
import { ThemeToggle } from "@/components/theme-toggle";
import { Footer } from "@/components/footer";
import Image from "next/image";
import { ExternalLink } from "lucide-react";
import Link from "next/link";
import { Menu, X } from "lucide-react";
import { SmoothScrollProvider } from "@/components/animations";
import {
  HeroSection,
  AboutSection,
  ExperienceSection,
  HireMeSection,
  ContributionSection
} from "@/components/sections";
import MobileBottomNav from "@/components/MobileBottomNav";
import { useMusicContext } from "@/components/MusicContext";



export default function Home() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);
  const { isPlaying } = useMusicContext();

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
    setMobileMenuOpen(false);
  };

  return (
    <SmoothScrollProvider>
      <div className="min-h-screen bg-light-almond dark:bg-dark-bg transition-colors">
      {/* Blur Overlay for Mobile Menu */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 md:hidden transition-all duration-300"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-6 lg:px-12 max-w-7xl mx-auto relative z-50">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-3 text-deep-charcoal dark:text-dark-text hover:opacity-80 transition-opacity">
          <div className={`w-10 h-10 relative ${isHydrated && isPlaying ? 'music-spin' : ''}`}>
            <Image
              src="/hero.png"
              alt="Arkit_k Logo"
              fill
              className="object-contain rounded-full"
            />
          </div>
          <span className="text-xl font-bold">Arkit_k</span>
        </Link>

        {/* Desktop Navigation Links */}
        <div className="hidden md:flex items-center space-x-8 text-deep-charcoal dark:text-dark-text">
          <button
            onClick={() => scrollToSection('about')}
            className="hover:text-accent-green transition-colors"
          >
            About
          </button>
          <button
            onClick={() => scrollToSection('experience')}
            className="hover:text-accent-green transition-colors"
          >
            Projects
          </button>

          <button
            onClick={() => scrollToSection('hire')}
            className="hover:text-accent-green transition-colors"
          >
            Hire Me
          </button>
          <button
            onClick={() => scrollToSection('contributions')}
            className="hover:text-accent-green transition-colors"
          >
            Contributions
          </button>
        </div>

        {/* Mobile Menu Button & Theme Toggle */}
        <div className="flex items-center gap-4">
          <ThemeToggle />
          <Link
            href="https://cal.com/arkit-karmokar-x0uyir/secret"
            className="p-2 bg-deep-charcoal/10 dark:bg-dark-text/10 rounded-lg hover:bg-deep-charcoal/20 dark:hover:bg-dark-text/20 transition-colors"
            target="_blank"
            rel="noopener noreferrer"
            title="Schedule a meeting"
          >
            <ExternalLink className="w-4 h-4 text-deep-charcoal dark:text-dark-text" />
          </Link>
          {/* Mobile menu button hidden - using bottom nav instead */}
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="hidden p-2 text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors"
          >
            {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation Menu - Hidden, using bottom nav instead */}
        {false && mobileMenuOpen && (
          <div className="absolute top-full left-0 right-0 bg-light-almond/95 dark:bg-dark-bg/95 backdrop-blur-md border-t border-deep-charcoal/10 dark:border-dark-text/10 md:hidden z-50 shadow-lg mobile-menu-enter">
            <div className="px-6 py-4 space-y-4">
              <button
                onClick={() => scrollToSection('about')}
                className="block w-full text-left text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors py-2"
              >
                About
              </button>
              <button
                onClick={() => scrollToSection('experience')}
                className="block w-full text-left text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors py-2"
              >
                Projects
              </button>

              <button
                onClick={() => scrollToSection('hire')}
                className="block w-full text-left text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors py-2"
              >
                Hire Me
              </button>
              <button
                onClick={() => scrollToSection('contributions')}
                className="block w-full text-left text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors py-2"
              >
                Contributions
              </button>
              <a
                href="mailto:<EMAIL>"
                className="block w-full bg-accent-green hover:bg-accent-green/80 text-deep-charcoal font-medium px-4 py-3 rounded-lg transition-colors text-center mt-4"
                onClick={() => setMobileMenuOpen(false)}
              >
                Contact
              </a>
            </div>
          </div>
        )}
      </nav>



      {/* Main Content */}
      <main className="overflow-x-hidden pb-20 md:pb-0">
        <HeroSection />
        <AboutSection />
        <ExperienceSection />
        <HireMeSection />
        <ContributionSection />
      </main>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav scrollToSection={scrollToSection} />

      {/* Footer */}
      <Footer />
      </div>
    </SmoothScrollProvider>
  );
}
